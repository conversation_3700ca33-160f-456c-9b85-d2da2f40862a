{
  "timestamp": "05/05/2025, 21:28:06",
  "ip": "::ffff:*************",
  "formFields": {
    "title": "Mr",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "full_name": "<PERSON>",
    "date_of_birth": "0101-12-09",
    "nationality": "German",
    "gender": "Male",
    "email_primary": "<EMAIL>",
    "email_secondary": "<EMAIL>",
    "phone_primary": " ***********",
    "phone_secondary": " ***********",
    "work_telephone": " *************",
    "home_telephone": " *************",
    "mobile_number": " ************",
    "skype_account": "john.doe",
    "website": "https://johndoe.com",
    "whatsapp_available": "yes",
    "time_zone": "Europe/Berlin",
    "address_line_1": "Main St 123",
    "address_line_2": "Apt 4",
    "city": "Berlin",
    "state_province": "Berlin",
    "postal_code": "10115",
    "country": "Germany",
    "current_location": "Berlin, Germany",
    "profile_type": "Freelancer",
    "business_name": "JD Translates",
    "company_name": "JD GmbH",
    "applying_as": "Translator",
    "plunet_profile_active": "yes",
    "native_language": "German",
    "native_accent": "None",
    "other_languages": "English, French",
    "source_languages": "English",
    "target_languages": "German",
    "language_pairs": "EN-DE",
    "communication_language": "English",
    "specializations": "Legal, Medical",
    "services_provided": "Translation, Proofreading",
    "working_fields": "IT, Marketing",
    "supplier_for": "Translation",
    "scope_of_work": "Freelance Translator",
    "office_hours": "9am-5pm",
    "works_weekends": "yes",
    "weekend_availability": "Saturday",
    "voice_collection_interest": "no",
    "subtitling_interest": "yes",
    "transcription_experience": "moderate",
    "certified_court_interpreter": "no",
    "adult_content_ok": "no",
    "daily_translation_volume": "3000",
    "rate_per_word": "0.08",
    "rate_per_hour_editing": "25",
    "rate_per_hour_other": "30",
    "medical_translation_percent": "20",
    "cat_tools": "Trados, MemoQ",
    "qa_tools": "Verifika",
    "software_experience": "Trados, Phrase TMS",
    "has_trados_license": "yes",
    "has_memoq_experience": "yes",
    "has_phrase_tms_experience": "yes",
    "education": "MA in Translation",
    "seniority": "Senior",
    "years_experience": "10",
    "translation_degree_status": "Yes",
    "pemt_experience": "3 years",
    "willing_for_pemt": "yes",
    "mtpe_experience": "yes",
    "educational_background": "University of Berlin, ATA member",
    "translator_membership": "yes",
    "translator_membership_details": "ATA",
    "ata_membership": "yes",
    "recommendation_letters": "yes",
    "gdpr_consent": "Yes",
    "accept_code_of_conduct": "Yes",
    "application_source": "LinkedIn",
    "notes": "None",
    "internal_procedures": "None",
    "translator_selection_criteria": "None",
    "sample_translation_required": "no",
    "captcha": "FTgUy"
  },
  "uploadedFiles": []
}
---
