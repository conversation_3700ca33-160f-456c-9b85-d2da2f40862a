<?php
declare(strict_types=1);

/**
 * Simple PHP File API (single file)
 * Endpoints (via ?action=…):
 *   GET  api.php?action=tree&path=/sub/folder
 *   POST api.php?action=upload   (FormData: path, files[])
 *   POST api.php?action=mkdir    (JSON or form: parent, name)
 *   POST api.php?action=rename   (JSON or form: path, newName)
 *
 * Root: ./storage (created if missing)
 * NOTE: Dev-only, tighten security for production.
 */

/* --------------------------- CORS --------------------------- */
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
$allowed = ['http://localhost:5173', 'http://127.0.0.1:5173'];
if (in_array($origin, $allowed, true)) {
  header("Access-Control-Allow-Origin: $origin");
  header("Access-Control-Allow-Credentials: true");
} else {
  header("Access-Control-Allow-Origin: *"); // dev fallback
}
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') { http_response_code(204); exit; }
header('Content-Type: application/json; charset=utf-8');

/* ------------------------ CONFIG --------------------------- */
$BASE_DIR = __DIR__ . DIRECTORY_SEPARATOR . 'storage';
if (!is_dir($BASE_DIR)) {
  @mkdir($BASE_DIR, 0775, true);
}

/* ------------------------ Helpers -------------------------- */
function json_ok($data = []) {
  echo json_encode($data, JSON_UNESCAPED_SLASHES);
  exit;
}
function json_err($message, int $code = 400) {
  http_response_code($code);
  echo json_encode(['ok' => false, 'error' => $message], JSON_UNESCAPED_SLASHES);
  exit;
}

/** normalize.relative path: remove //, ./, handle .. safely */
function normalize_rel(string $p): string {
  $p = str_replace('\\', '/', $p);
  $parts = [];
  foreach (explode('/', $p) as $seg) {
    if ($seg === '' || $seg === '.') continue;
    if ($seg === '..') { array_pop($parts); continue; }
    $parts[] = $seg;
  }
  return '/' . implode('/', $parts);
}

/** Join base with a relative (safe for non-existing targets) */
function join_abs(string $base, string $rel): string {
  $rel = ltrim(normalize_rel($rel), '/');        // '/a/b' -> 'a/b'
  return rtrim($base, DIRECTORY_SEPARATOR) . ($rel ? DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $rel) : '');
}

/** Safety: ensure a single name (no slashes, no traversal) */
function sanitize_name(string $name): string {
  $name = trim($name);
  if ($name === '' || $name === '.' || $name === '..') json_err('Invalid name');
  if (preg_match('/[\\\\\\/]/', $name)) json_err('Name cannot contain slashes');
  return $name;
}

/** Build a node (folder or file) recursively */
function node_for(string $abs, string $rel): array {
  $isDir = is_dir($abs);
  $name = ($rel === '/') ? 'root' : basename($abs);

  if ($isDir) {
    $children = [];
    $items = @scandir($abs) ?: [];
    foreach ($items as $entry) {
      if ($entry === '.' || $entry === '..') continue;
      $childAbs = $abs . DIRECTORY_SEPARATOR . $entry;
      $childRel = rtrim($rel, '/') . '/' . $entry;
      $children[] = node_for($childAbs, normalize_rel($childRel));
    }
    // Folders first, natural sort
    usort($children, function ($a, $b) {
      if ($a['type'] !== $b['type']) return $a['type'] === 'folder' ? -1 : 1;
      return strnatcasecmp($a['name'], $b['name']);
    });
    return [
      'name'     => $name,
      'path'     => $rel,
      'type'     => 'folder',
      'children' => $children,
    ];
  }

  return [
    'name'       => $name,
    'path'       => $rel,
    'type'       => 'file',
    'size'       => @filesize($abs) ?: 0,
    'modifiedAt' => @date('c', @filemtime($abs) ?: time()),
  ];
}

/** Read JSON body or fallback to POST form */
function read_input(): array {
  $raw = file_get_contents('php://input');
  if ($raw) {
    $data = json_decode($raw, true);
    if (is_array($data)) return $data;
  }
  return $_POST ?? [];
}

/* ------------------------- Router -------------------------- */
$action = $_GET['action'] ?? ($_POST['action'] ?? '');

try {
  switch ($action) {
    /* -------- GET TREE -------- */
    case 'tree': {
      $rel = isset($_GET['path']) ? normalize_rel((string)$_GET['path']) : '/';
      $abs = ($rel === '/') ? $BASE_DIR : join_abs($BASE_DIR, $rel);
      if (!file_exists($abs)) json_err('Path not found: ' . $rel, 404);
      json_ok(node_for($abs, $rel));
    }

    /* ------- CREATE FOLDER ----- */
    case 'mkdir': {
      $in = read_input();
      $parent = normalize_rel((string)($in['parent'] ?? '/'));
      $name   = sanitize_name((string)($in['name'] ?? ''));
      $parentAbs = ($parent === '/') ? $BASE_DIR : join_abs($BASE_DIR, $parent);
      if (!is_dir($parentAbs)) json_err('Parent directory not found', 404);

      $targetAbs = $parentAbs . DIRECTORY_SEPARATOR . $name;
      if (file_exists($targetAbs)) json_err('File or folder already exists', 409);
      if (!@mkdir($targetAbs, 0775, true)) json_err('Failed to create folder');

      json_ok(['ok' => true, 'created' => normalize_rel(rtrim($parent, '/') . '/' . $name)]);
    }

    /* --------- RENAME ---------- */
    case 'rename': {
      $in = read_input();
      $relPath = normalize_rel((string)($in['path'] ?? ''));
      $newName = sanitize_name((string)($in['newName'] ?? ''));
      if ($relPath === '/') json_err('Cannot rename root');

      $oldAbs = join_abs($BASE_DIR, $relPath);
      if (!file_exists($oldAbs)) json_err('Path not found', 404);

      $parentRel = normalize_rel(dirname($relPath));
      $parentAbs = ($parentRel === '/') ? $BASE_DIR : join_abs($BASE_DIR, $parentRel);
      $newAbs = $parentAbs . DIRECTORY_SEPARATOR . $newName;
      if (file_exists($newAbs)) json_err('Target exists', 409);

      if (!@rename($oldAbs, $newAbs)) json_err('Rename failed');
      json_ok(['ok' => true, 'oldPath' => $relPath, 'newPath' => normalize_rel(rtrim($parentRel, '/') . '/' . $newName)]);
    }

    /* --------- UPLOAD ---------- */
    case 'upload': {
      // FormData: path (string), files (one or many)
      $targetRel = normalize_rel((string)($_POST['path'] ?? '/'));
      $targetAbs = ($targetRel === '/') ? $BASE_DIR : join_abs($BASE_DIR, $targetRel);
      if (!is_dir($targetAbs)) json_err('Target directory not found', 404);

      if (!isset($_FILES['files'])) json_err('No files uploaded');
      $files = reformat_files_array($_FILES['files']);

      $saved = [];
      foreach ($files as $f) {
        if ($f['error'] !== UPLOAD_ERR_OK) json_err('Upload error code: ' . $f['error']);
        $name = sanitize_name($f['name']); // already the client-side rename
        $dest = $targetAbs . DIRECTORY_SEPARATOR . $name;

        // Optional: if exists, make a unique name
        if (file_exists($dest)) {
          $dot = strrpos($name, '.');
          if ($dot !== false) {
            $base = substr($name, 0, $dot);
            $ext  = substr($name, $dot);
          } else {
            $base = $name;
            $ext  = '';
          }
          $name = $base . '-' . uniqid() . $ext;
          $dest = $targetAbs . DIRECTORY_SEPARATOR . $name;
        }

        if (!@move_uploaded_file($f['tmp_name'], $dest)) json_err('Failed to move uploaded file');
        $saved[] = normalize_rel(rtrim($targetRel, '/') . '/' . $name);
      }

      json_ok(['ok' => true, 'saved' => $saved]);
    }

    default:
      json_err('Unknown action', 404);
  }
} catch (Throwable $e) {
  json_err($e->getMessage(), 400);
}

/* ---------- Normalize $_FILES for multi-upload ---------- */
function reformat_files_array(array $file): array {
  $out = [];
  if (is_array($file['name'])) {
    $count = count($file['name']);
    for ($i = 0; $i < $count; $i++) {
      $out[] = [
        'name'     => $file['name'][$i],
        'type'     => $file['type'][$i],
        'tmp_name' => $file['tmp_name'][$i],
        'error'    => $file['error'][$i],
        'size'     => $file['size'][$i],
      ];
    }
  } else {
    $out[] = $file;
  }
  return $out;
}
